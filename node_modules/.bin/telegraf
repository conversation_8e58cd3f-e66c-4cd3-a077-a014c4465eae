#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Documents/Coding/Test/Bhasini/node_modules/.pnpm/telegraf@4.16.3/node_modules/telegraf/lib/node_modules:/Users/<USER>/Documents/Coding/Test/Bhasini/node_modules/.pnpm/telegraf@4.16.3/node_modules/telegraf/node_modules:/Users/<USER>/Documents/Coding/Test/Bhasini/node_modules/.pnpm/telegraf@4.16.3/node_modules:/Users/<USER>/Documents/Coding/Test/Bhasini/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Documents/Coding/Test/Bhasini/node_modules/.pnpm/telegraf@4.16.3/node_modules/telegraf/lib/node_modules:/Users/<USER>/Documents/Coding/Test/Bhasini/node_modules/.pnpm/telegraf@4.16.3/node_modules/telegraf/node_modules:/Users/<USER>/Documents/Coding/Test/Bhasini/node_modules/.pnpm/telegraf@4.16.3/node_modules:/Users/<USER>/Documents/Coding/Test/Bhasini/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.pnpm/telegraf@4.16.3/node_modules/telegraf/lib/cli.mjs" "$@"
else
  exec node  "$basedir/../.pnpm/telegraf@4.16.3/node_modules/telegraf/lib/cli.mjs" "$@"
fi
