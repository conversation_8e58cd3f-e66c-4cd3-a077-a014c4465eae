export interface BaseDataContentBlock {
    mime_type?: string;
    metadata?: Record<string, unknown>;
}
export interface URLContentBlock extends BaseDataContentBlock {
    type: "image" | "audio" | "file";
    source_type: "url";
    url: string;
}
export interface Base64ContentBlock extends BaseDataContentBlock {
    type: "image" | "audio" | "file";
    source_type: "base64";
    data: string;
}
export interface PlainTextContentBlock extends BaseDataContentBlock {
    type: "file" | "text";
    source_type: "text";
    text: string;
}
export interface IDContentBlock extends BaseDataContentBlock {
    type: "image" | "audio" | "file";
    source_type: "id";
    id: string;
}
export type DataContentBlock = URLContentBlock | Base64ContentBlock | PlainTextContentBlock | IDContentBlock;
export type StandardImageBlock = (URLContentBlock | Base64ContentBlock | IDContentBlock) & {
    type: "image";
};
export type StandardAudioBlock = (URLContentBlock | Base64ContentBlock | IDContentBlock) & {
    type: "audio";
};
export type StandardFileBlock = (URLContentBlock | Base64ContentBlock | IDContentBlock | PlainTextContentBlock) & {
    type: "file";
};
export type StandardTextBlock = PlainTextContentBlock & {
    type: "text";
};
export type DataContentBlockType = DataContentBlock["type"];
export declare function isDataContentBlock(content_block: object): content_block is DataContentBlock;
export declare function isURLContentBlock(content_block: object): content_block is URLContentBlock;
export declare function isBase64ContentBlock(content_block: object): content_block is Base64ContentBlock;
export declare function isPlainTextContentBlock(content_block: object): content_block is PlainTextContentBlock;
export declare function isIDContentBlock(content_block: object): content_block is IDContentBlock;
export declare function convertToOpenAIImageBlock(content_block: URLContentBlock | Base64ContentBlock): {
    type: string;
    image_url: {
        url: string;
    };
};
/**
 * Utility function for ChatModelProviders. Parses a mime type into a type, subtype, and parameters.
 *
 * @param mime_type - The mime type to parse.
 * @returns An object containing the type, subtype, and parameters.
 */
export declare function parseMimeType(mime_type: string): {
    type: string;
    subtype: string;
    parameters: Record<string, string>;
};
/**
 * Utility function for ChatModelProviders. Parses a base64 data URL into a typed array or string.
 *
 * @param dataUrl - The base64 data URL to parse.
 * @param asTypedArray - Whether to return the data as a typed array.
 * @returns An object containing the parsed data and mime type, or undefined if the data URL is invalid.
 */
export declare function parseBase64DataUrl({ dataUrl, asTypedArray, }: {
    dataUrl: string;
    asTypedArray: true;
}): {
    data: Uint8Array;
    mime_type: string;
} | undefined;
/**
 * Utility function for ChatModelProviders. Parses a base64 data URL into a typed array or string.
 *
 * @param dataUrl - The base64 data URL to parse.
 * @param asTypedArray - Whether to return the data as a typed array.
 * @returns The parsed data and mime type, or undefined if the data URL is invalid.
 */
export declare function parseBase64DataUrl({ dataUrl, asTypedArray, }: {
    dataUrl: string;
    asTypedArray?: false;
}): {
    data: string;
    mime_type: string;
} | undefined;
/**
 * A bag of provider-specific content block types.
 *
 * Allows implementations of {@link StandardContentBlockConverter} and related to be defined only in
 * terms of the types they support. Also allows for forward compatibility as the set of known
 * standard types grows, as the set of types can be extended without breaking existing
 * implementations of the aforementioned interfaces.
 */
export type ProviderFormatTypes<TextFormat = unknown, ImageFormat = unknown, AudioFormat = unknown, FileFormat = unknown> = {
    text: TextFormat;
    image: ImageFormat;
    audio: AudioFormat;
    file: FileFormat;
};
/**
 * Utility interface for converting between standard and provider-specific data content blocks, to be
 * used when implementing chat model providers.
 *
 * Meant to be used with {@link convertToProviderContentBlock} and
 * {@link convertToStandardContentBlock} rather than being consumed directly.
 */
export interface StandardContentBlockConverter<Formats extends Partial<ProviderFormatTypes>> {
    /**
     * The name of the provider type that corresponds to the provider-specific content block types
     * that this converter supports.
     */
    providerName: string;
    /**
     * Convert from a standard image block to a provider's proprietary image block format.
     * @param block - The standard image block to convert.
     * @returns The provider image block.
     */
    fromStandardImageBlock?(block: StandardImageBlock): Formats["image"];
    /**
     * Convert from a standard audio block to a provider's proprietary audio block format.
     * @param block - The standard audio block to convert.
     * @returns The provider audio block.
     */
    fromStandardAudioBlock?(block: StandardAudioBlock): Formats["audio"];
    /**
     * Convert from a standard file block to a provider's proprietary file block format.
     * @param block - The standard file block to convert.
     * @returns The provider file block.
     */
    fromStandardFileBlock?(block: StandardFileBlock): Formats["file"];
    /**
     * Convert from a standard text block to a provider's proprietary text block format.
     * @param block - The standard text block to convert.
     * @returns The provider text block.
     */
    fromStandardTextBlock?(block: StandardTextBlock): Formats["text"];
}
/**
 * Convert from a standard data content block to a provider's proprietary data content block format.
 *
 * Don't override this method. Instead, override the more specific conversion methods and use this
 * method unmodified.
 *
 * @param block - The standard data content block to convert.
 * @returns The provider data content block.
 * @throws An error if the standard data content block type is not supported.
 */
export declare function convertToProviderContentBlock<Formats extends Partial<ProviderFormatTypes>>(block: DataContentBlock, converter: StandardContentBlockConverter<Formats>): Formats[keyof Formats];
