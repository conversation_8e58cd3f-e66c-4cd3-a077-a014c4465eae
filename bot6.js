import { Telegraf } from "telegraf";
import ffmpeg from "fluent-ffmpeg";
import {
    writeFileSync,
    readFileSync,
    createReadStream,
    existsSync,
    unlinkSync,
} from "fs";
import fetch from "node-fetch";
import { v4 as uuidv4 } from "uuid";
import { OpenAI } from "openai";
import path from "path";
import { config as dotenvConfig } from "dotenv";

dotenvConfig();

const bot = new Telegraf(process.env.TELEGRAM_BOT_TOKEN);
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

// Store user language preferences - now with both input and output
const userLanguages = new Map();

async function convertOgaToWav(inputPath, outputPath) {
    return new Promise((resolve, reject) => {
        ffmpeg(inputPath)
            .audioCodec("pcm_s16le")
            .audioChannels(1)
            .audioFrequency(16000)
            .format("wav")
            .save(outputPath)
            .on("end", () => resolve())
            .on("error", reject);
    });
}

// Hindi Speech-to-Text
async function getTextFromSpeechHindi(base64Audio) {
    const response = await fetch(
        "https://anuvaad-backend.bhashini.co.in/v1/pipeline",
        {
            method: "POST",
            headers: {
                "content-type": "application/json",
            },
            body: JSON.stringify({
                pipelineTasks: [
                    {
                        taskType: "asr",
                        config: {
                            language: { sourceLanguage: "hi" },
                            serviceId: "ai4bharat/conformer-hi-gpu--t4",
                            preProcessors: [],
                            postProcessors: ["itn"],
                        },
                    },
                ],
                inputData: {
                    input: [{ source: "" }],
                    audio: [{ audioContent: base64Audio }],
                },
            }),
        }
    );

    const result = await response.json();
    return result.pipelineResponse[0].output[0].source;
}

// English Speech-to-Text
async function getTextFromSpeechEnglish(base64Audio) {
    const response = await fetch(
        "https://anuvaad-backend.bhashini.co.in/v1/pipeline",
        {
            method: "POST",
            headers: {
                "content-type": "application/json",
            },
            body: JSON.stringify({
                pipelineTasks: [
                    {
                        taskType: "asr",
                        config: {
                            language: { sourceLanguage: "en" },
                            serviceId: "ai4bharat/whisper-medium-en--gpu--t4",
                            preProcessors: [],
                            postProcessors: ["itn"],
                        },
                    },
                ],
                inputData: {
                    input: [{ source: "" }],
                    audio: [{ audioContent: base64Audio }],
                },
            }),
        }
    );

    const result = await response.json();
    return result.pipelineResponse[0].output[0].source;
}

// Translation functions
async function translateHindiToEnglish(text) {
    const response = await fetch(
        "https://anuvaad-backend.bhashini.co.in/v1/pipeline",
        {
            method: "POST",
            headers: { "content-type": "application/json" },
            body: JSON.stringify({
                pipelineTasks: [
                    {
                        taskType: "translation",
                        config: {
                            language: {
                                sourceLanguage: "hi",
                                targetLanguage: "en",
                                sourceScriptCode: "Deva",
                                targetScriptCode: "Latn",
                            },
                            serviceId: "ai4bharat/indictrans-v2-all-gpu--t4",
                        },
                    },
                ],
                inputData: { input: [{ source: text }], audio: [] },
            }),
        }
    );

    const result = await response.json();
    return result.pipelineResponse[0].output[0].target;
}

async function translateEnglishToHindi(text) {
    const response = await fetch(
        "https://anuvaad-backend.bhashini.co.in/v1/pipeline",
        {
            method: "POST",
            headers: { "content-type": "application/json" },
            body: JSON.stringify({
                pipelineTasks: [
                    {
                        taskType: "translation",
                        config: {
                            language: {
                                sourceLanguage: "en",
                                targetLanguage: "hi",
                                sourceScriptCode: "Latn",
                                targetScriptCode: "Deva",
                            },
                            serviceId: "ai4bharat/indictrans-v2-all-gpu--t4",
                        },
                    },
                ],
                inputData: { input: [{ source: text }], audio: [] },
            }),
        }
    );

    const result = await response.json();
    return result.pipelineResponse[0].output[0].target;
}

async function getGptResponse(prompt) {
    const response = await openai.chat.completions.create({
        model: "gpt-4o-mini",
        messages: [{ role: "user", content: prompt }],
    });

    return response.choices[0].message.content;
}

// Hindi Text-to-Speech
async function getSpeechFromTextHindi(text) {
    const response = await fetch(
        "https://anuvaad-backend.bhashini.co.in/v1/pipeline",
        {
            method: "POST",
            headers: {
                Accept: "application/json, text/plain, */*",
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                pipelineTasks: [
                    {
                        taskType: "tts",
                        config: {
                            language: { sourceLanguage: "hi" },
                            serviceId: "Bhashini/IITM/TTS",
                            gender: "female",
                            preProcessors: [],
                            postProcessors: [],
                        },
                    },
                ],
                inputData: {
                    input: [{ source: text }],
                    audio: [{ audioContent: "" }],
                },
            }),
        }
    );

    const result = await response.json();

    if (
        !result.pipelineResponse ||
        !result.pipelineResponse[0] ||
        !result.pipelineResponse[0].audio
    ) {
        console.error(
            "❌ Hindi TTS API Response:",
            JSON.stringify(result, null, 2)
        );
        throw new Error("Invalid Hindi TTS API response structure");
    }

    return result.pipelineResponse[0].audio[0].audioContent;
}

// English Text-to-Speech using OpenAI's TTS API
async function getSpeechFromTextEnglish(text) {
    try {
        const response = await openai.audio.speech.create({
            model: "tts-1",
            voice: "alloy",
            input: text,
            response_format: "wav",
        });

        const buffer = Buffer.from(await response.arrayBuffer());
        return buffer.toString("base64");
    } catch (error) {
        console.error("❌ English TTS Error:", error);
        throw new Error("Failed to generate English speech");
    }
}

async function saveBase64ToWav(base64, wavPath) {
    const buffer = Buffer.from(base64, "base64");
    writeFileSync(wavPath, buffer);
}

async function convertWavToOgg(wavPath, oggPath) {
    return new Promise((resolve, reject) => {
        ffmpeg(wavPath)
            .inputFormat("wav")
            .audioCodec("libopus")
            .audioFrequency(48000)
            .audioChannels(1)
            .format("ogg")
            .on("start", (cmd) => console.log("🛠 FFmpeg started:", cmd))
            .on("error", (err, stdout, stderr) => {
                console.error("❌ FFmpeg error:", err.message);
                reject(err);
            })
            .on("end", () => {
                console.log("✅ Conversion complete:", oggPath);
                resolve();
            })
            .save(oggPath);
    });
}

// Enhanced language selection command
bot.command("change_language", (ctx) => {
    const keyboard = {
        inline_keyboard: [
            [
                {
                    text: "🎤 Choose Input Language",
                    callback_data: "select_input",
                },
            ],
            [
                {
                    text: "🔊 Choose Output Language",
                    callback_data: "select_output",
                },
            ],
            [
                {
                    text: "📋 View Current Settings",
                    callback_data: "view_settings",
                },
            ],
        ],
    };

    ctx.reply("🌐 Language Settings:", { reply_markup: keyboard });
});

// Input language selection
bot.action("select_input", (ctx) => {
    const keyboard = {
        inline_keyboard: [
            [
                { text: "🇮🇳 Hindi Input", callback_data: "input_hindi" },
                { text: "🇺🇸 English Input", callback_data: "input_english" },
            ],
            [{ text: "⬅️ Back", callback_data: "back_to_main" }],
        ],
    };

    ctx.editMessageText("🎤 Choose your input language:", {
        reply_markup: keyboard,
    });
});

// Output language selection
bot.action("select_output", (ctx) => {
    const keyboard = {
        inline_keyboard: [
            [
                { text: "🇮🇳 Hindi Output", callback_data: "output_hindi" },
                { text: "🇺🇸 English Output", callback_data: "output_english" },
            ],
            [{ text: "⬅️ Back", callback_data: "back_to_main" }],
        ],
    };

    ctx.editMessageText("🔊 Choose your output language:", {
        reply_markup: keyboard,
    });
});

// Handle input language selection
bot.action("input_hindi", (ctx) => {
    const userId = ctx.from.id;
    const currentSettings = userLanguages.get(userId) || {
        input: "hindi",
        output: "hindi",
    };
    currentSettings.input = "hindi";
    userLanguages.set(userId, currentSettings);

    ctx.answerCbQuery();
    ctx.editMessageText(
        "✅ Input language set to Hindi! 🇮🇳\nYou can now send voice messages or text in Hindi.\nUse /change_language to modify other settings."
    );
});

bot.action("input_english", (ctx) => {
    const userId = ctx.from.id;
    const currentSettings = userLanguages.get(userId) || {
        input: "hindi",
        output: "hindi",
    };
    currentSettings.input = "english";
    userLanguages.set(userId, currentSettings);

    ctx.answerCbQuery();
    ctx.editMessageText(
        "✅ Input language set to English! 🇺🇸\nYou can now send voice messages or text in English.\nUse /change_language to modify other settings."
    );
});

// Handle output language selection
bot.action("output_hindi", (ctx) => {
    const userId = ctx.from.id;
    const currentSettings = userLanguages.get(userId) || {
        input: "hindi",
        output: "hindi",
    };
    currentSettings.output = "hindi";
    userLanguages.set(userId, currentSettings);

    ctx.answerCbQuery();
    ctx.editMessageText(
        "✅ Output language set to Hindi! 🇮🇳\nUse /change_language to modify other settings."
    );
});

bot.action("output_english", (ctx) => {
    const userId = ctx.from.id;
    const currentSettings = userLanguages.get(userId) || {
        input: "hindi",
        output: "hindi",
    };
    currentSettings.output = "english";
    userLanguages.set(userId, currentSettings);

    ctx.answerCbQuery();
    ctx.editMessageText(
        "✅ Output language set to English! 🇺🇸\nUse /change_language to modify other settings."
    );
});

// View current settings
bot.action("view_settings", (ctx) => {
    const userId = ctx.from.id;
    const settings = userLanguages.get(userId) || {
        input: "hindi",
        output: "hindi",
    };

    const inputFlag = settings.input === "hindi" ? "🇮🇳" : "🇺🇸";
    const outputFlag = settings.output === "hindi" ? "🇮🇳" : "🇺🇸";

    const keyboard = {
        inline_keyboard: [[{ text: "⬅️ Back", callback_data: "back_to_main" }]],
    };

    ctx.editMessageText(
        `📋 Current Language Settings:\n\n` +
            `🎤 Input: ${inputFlag} ${
                settings.input.charAt(0).toUpperCase() + settings.input.slice(1)
            }\n` +
            `🔊 Output: ${outputFlag} ${
                settings.output.charAt(0).toUpperCase() +
                settings.output.slice(1)
            }\n\n` +
            `📝 Workflow: ${settings.input} Text/Voice → GPT → ${settings.output} Voice\n\n` +
            `💡 You can send both text messages and voice messages in your selected input language!`,
        { reply_markup: keyboard }
    );
});

// Back to main menu
bot.action("back_to_main", (ctx) => {
    const keyboard = {
        inline_keyboard: [
            [
                {
                    text: "🎤 Choose Input Language",
                    callback_data: "select_input",
                },
            ],
            [
                {
                    text: "🔊 Choose Output Language",
                    callback_data: "select_output",
                },
            ],
            [
                {
                    text: "📋 View Current Settings",
                    callback_data: "view_settings",
                },
            ],
        ],
    };

    ctx.editMessageText("🌐 Language Settings:", { reply_markup: keyboard });
});

// Start command
bot.start((ctx) => {
    const userId = ctx.from.id;
    // Set default languages to Hindi for both input and output
    userLanguages.set(userId, { input: "hindi", output: "hindi" });

    ctx.reply(
        "🎙️ Welcome to the Ultimate Voice & Text AI Bot!\n\n" +
            "🗣️ Send me a voice message OR type a text message and I'll respond with AI-generated audio!\n\n" +
            "🌐 Default: Hindi Input → Hindi Output\n" +
            "Use /change_language to customize both input and output languages.\n\n" +
            "📝 Available input methods:\n" +
            "• 🎤 Voice messages in your selected language\n" +
            "• ⌨️ Text messages in your selected language\n\n" +
            "🔊 Available workflows:\n" +
            "• 🇮🇳→🇮🇳 Hindi Text/Voice → GPT → Hindi Voice\n" +
            "• 🇺🇸→🇺🇸 English Text/Voice → GPT → English Voice\n" +
            "• 🇮🇳→🇺🇸 Hindi Text/Voice → GPT → English Voice\n" +
            "• 🇺🇸→🇮🇳 English Text/Voice → GPT → Hindi Voice"
    );
});

// Core processing function for both text and voice inputs
async function processUserInput(ctx, inputText, inputType = "text") {
    const userId = ctx.from.id;
    const settings = userLanguages.get(userId) || {
        input: "hindi",
        output: "hindi",
    };

    const inputFlag = settings.input === "hindi" ? "🇮🇳" : "🇺🇸";
    const outputFlag = settings.output === "hindi" ? "🇮🇳" : "🇺🇸";
    const inputIcon = inputType === "voice" ? "🎤" : "⌨️";

    // Send thinking message with current workflow
    const thinkingMessage = await ctx.reply(
        `💬 Thinking... (${inputIcon}${inputFlag}→${outputFlag})`
    );

    try {
        let englishPrompt;

        // Step 1: Process input based on input language
        if (settings.input === "hindi") {
            // If input is Hindi, translate to English for GPT
            englishPrompt = await translateHindiToEnglish(inputText);
            console.log("🔁 Hindi→EN:", inputText, "→", englishPrompt);
        } else {
            // If input is English, use directly
            englishPrompt = inputText;
            console.log("🔁 EN Prompt:", englishPrompt);
        }

        // Step 2: Get GPT response
        const gptReply = await getGptResponse(englishPrompt);
        console.log("🤖 GPT Output:", gptReply);

        let finalText;
        let ttsBase64;

        // Step 3: Translate and generate speech based on output language
        if (settings.output === "hindi") {
            finalText = await translateEnglishToHindi(gptReply);
            console.log("📝 Hindi Output:", finalText);
            ttsBase64 = await getSpeechFromTextHindi(finalText);
        } else {
            finalText = gptReply; // Use English text directly
            console.log("📝 English Output:", finalText);
            ttsBase64 = await getSpeechFromTextEnglish(finalText);
        }

        // Generate audio file
        const uid = uuidv4();
        const wavPath = `./tmp/${uid}.wav`;
        const oggPath = `./tmp/${uid}.ogg`;

        await saveBase64ToWav(ttsBase64, wavPath);
        await convertWavToOgg(wavPath, oggPath);

        // Delete thinking message
        await ctx.deleteMessage(thinkingMessage.message_id);

        // Send voice response with appropriate caption
        const caption = `${inputIcon}${inputFlag} Input: "${inputText}"\n🔊${outputFlag} Output: ${finalText}`;

        await ctx.replyWithVoice(
            { source: createReadStream(oggPath) },
            { caption: caption }
        );

        // Cleanup files
        [wavPath, oggPath].forEach((f) => existsSync(f) && unlinkSync(f));
    } catch (err) {
        console.error("❌ Error:", err);
        // Delete thinking message on error
        try {
            await ctx.deleteMessage(thinkingMessage.message_id);
        } catch (deleteErr) {
            console.error("Failed to delete thinking message:", deleteErr);
        }
        ctx.reply("❌ Something went wrong processing your message.");
    }
}

// Enhanced text message handler
bot.on("text", async (ctx) => {
    const text = ctx.message.text;

    // Skip if it's a command
    if (text.startsWith("/")) return;

    const userId = ctx.from.id;
    const settings = userLanguages.get(userId) || {
        input: "hindi",
        output: "hindi",
    };

    console.log(`📝 Text Input (${settings.input}):`, text);

    await processUserInput(ctx, text, "text");
});

// Enhanced voice message handler
bot.on("voice", async (ctx) => {
    const userId = ctx.from.id;
    const settings = userLanguages.get(userId) || {
        input: "hindi",
        output: "hindi",
    };

    const fileId = ctx.message.voice.file_id;
    const fileLink = await ctx.telegram.getFileLink(fileId);
    const uid = uuidv4();
    const ogaPath = `./tmp/${uid}.oga`;
    const wavPath = `./tmp/${uid}.wav`;

    const res = await fetch(fileLink.href);
    const buffer = await res.buffer();
    writeFileSync(ogaPath, buffer);

    try {
        await convertOgaToWav(ogaPath, wavPath);
        const audioBase64 = readFileSync(wavPath).toString("base64");

        let recognizedText;

        // Step 1: Speech-to-Text based on input language
        if (settings.input === "hindi") {
            recognizedText = await getTextFromSpeechHindi(audioBase64);
            console.log("🗣 Hindi STT:", recognizedText);
        } else {
            recognizedText = await getTextFromSpeechEnglish(audioBase64);
            console.log("🗣 English STT:", recognizedText);
        }

        // Process the recognized text through the same pipeline as text input
        await processUserInput(ctx, recognizedText, "voice");
    } catch (err) {
        console.error("❌ Voice processing error:", err);
        ctx.reply("❌ Something went wrong processing your voice message.");
    } finally {
        // Cleanup voice files
        [ogaPath, wavPath].forEach((f) => existsSync(f) && unlinkSync(f));
    }
});

bot.launch();
console.log("🎙️ Ultimate Voice & Text AI Bot is running...");
