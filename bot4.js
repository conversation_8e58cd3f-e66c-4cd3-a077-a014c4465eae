import { Telegraf } from "telegraf";
import ffmpeg from "fluent-ffmpeg";
import {
    writeFileSync,
    readFileSync,
    createReadStream,
    existsSync,
    unlinkSync,
} from "fs";
import fetch from "node-fetch";
import { v4 as uuidv4 } from "uuid";
import { OpenAI } from "openai";
import path from "path";
import { config as dotenvConfig } from "dotenv";

dotenvConfig();

const bot = new Telegraf(process.env.TELEGRAM_BOT_TOKEN);
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

// Store user language preferences
const userLanguages = new Map();

async function convertOgaToWav(inputPath, outputPath) {
    return new Promise((resolve, reject) => {
        ffmpeg(inputPath)
            .audioCodec("pcm_s16le")
            .audioChannels(1)
            .audioFrequency(16000)
            .format("wav")
            .save(outputPath)
            .on("end", () => resolve())
            .on("error", reject);
    });
}

// Hindi Speech-to-Text
async function getTextFromSpeechHindi(base64Audio) {
    const response = await fetch(
        "https://anuvaad-backend.bhashini.co.in/v1/pipeline",
        {
            method: "POST",
            headers: {
                "content-type": "application/json",
            },
            body: JSON.stringify({
                pipelineTasks: [
                    {
                        taskType: "asr",
                        config: {
                            language: { sourceLanguage: "hi" },
                            serviceId: "ai4bharat/conformer-hi-gpu--t4",
                            preProcessors: [],
                            postProcessors: ["itn"],
                        },
                    },
                ],
                inputData: {
                    input: [{ source: "" }],
                    audio: [{ audioContent: base64Audio }],
                },
            }),
        }
    );

    const result = await response.json();
    return result.pipelineResponse[0].output[0].source;
}

// English Speech-to-Text
async function getTextFromSpeechEnglish(base64Audio) {
    const response = await fetch(
        "https://anuvaad-backend.bhashini.co.in/v1/pipeline",
        {
            method: "POST",
            headers: {
                "content-type": "application/json",
            },
            body: JSON.stringify({
                pipelineTasks: [
                    {
                        taskType: "asr",
                        config: {
                            language: { sourceLanguage: "en" },
                            serviceId: "ai4bharat/whisper-medium-en--gpu--t4",
                            preProcessors: [],
                            postProcessors: ["itn"],
                        },
                    },
                ],
                inputData: {
                    input: [{ source: "" }],
                    audio: [{ audioContent: base64Audio }],
                },
            }),
        }
    );

    const result = await response.json();
    return result.pipelineResponse[0].output[0].source;
}

async function translateHindiToEnglish(text) {
    const response = await fetch(
        "https://anuvaad-backend.bhashini.co.in/v1/pipeline",
        {
            method: "POST",
            headers: { "content-type": "application/json" },
            body: JSON.stringify({
                pipelineTasks: [
                    {
                        taskType: "translation",
                        config: {
                            language: {
                                sourceLanguage: "hi",
                                targetLanguage: "en",
                                sourceScriptCode: "Deva",
                                targetScriptCode: "Latn",
                            },
                            serviceId: "ai4bharat/indictrans-v2-all-gpu--t4",
                        },
                    },
                ],
                inputData: { input: [{ source: text }], audio: [] },
            }),
        }
    );

    const result = await response.json();
    return result.pipelineResponse[0].output[0].target;
}

async function getGptResponse(prompt) {
    const response = await openai.chat.completions.create({
        model: "gpt-4o-mini",
        messages: [{ role: "user", content: prompt }],
    });

    return response.choices[0].message.content;
}

async function translateEnglishToHindi(text) {
    const response = await fetch(
        "https://anuvaad-backend.bhashini.co.in/v1/pipeline",
        {
            method: "POST",
            headers: { "content-type": "application/json" },
            body: JSON.stringify({
                pipelineTasks: [
                    {
                        taskType: "translation",
                        config: {
                            language: {
                                sourceLanguage: "en",
                                targetLanguage: "hi",
                                sourceScriptCode: "Latn",
                                targetScriptCode: "Deva",
                            },
                            serviceId: "ai4bharat/indictrans-v2-all-gpu--t4",
                        },
                    },
                ],
                inputData: { input: [{ source: text }], audio: [] },
            }),
        }
    );

    const result = await response.json();
    return result.pipelineResponse[0].output[0].target;
}

async function getSpeechFromTextHindi(text) {
    const response = await fetch(
        "https://anuvaad-backend.bhashini.co.in/v1/pipeline",
        {
            method: "POST",
            headers: {
                Accept: "application/json, text/plain, */*",
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                pipelineTasks: [
                    {
                        taskType: "tts",
                        config: {
                            language: { sourceLanguage: "hi" },
                            serviceId: "Bhashini/IITM/TTS",
                            gender: "female",
                            preProcessors: [],
                            postProcessors: [],
                        },
                    },
                ],
                inputData: {
                    input: [{ source: text }],
                    audio: [{ audioContent: "" }],
                },
            }),
        }
    );

    const result = await response.json();

    // Add error handling and debugging
    if (
        !result.pipelineResponse ||
        !result.pipelineResponse[0] ||
        !result.pipelineResponse[0].audio
    ) {
        console.error("❌ TTS API Response:", JSON.stringify(result, null, 2));
        throw new Error("Invalid TTS API response structure");
    }

    return result.pipelineResponse[0].audio[0].audioContent;
}

async function saveBase64ToWav(base64, wavPath) {
    const buffer = Buffer.from(base64, "base64");
    writeFileSync(wavPath, buffer);
}

async function convertWavToOgg(wavPath, oggPath) {
    return new Promise((resolve, reject) => {
        ffmpeg(wavPath)
            .inputFormat("wav")
            .audioCodec("libopus") // ✅ use opus instead of vorbis for better Telegram compatibility
            .audioFrequency(48000) // ✅ resample to 48kHz for compatibility
            .audioChannels(1)
            .format("ogg")
            .on("start", (cmd) => console.log("🛠 FFmpeg started:", cmd))
            .on("error", (err, stdout, stderr) => {
                console.error("❌ FFmpeg error:", err.message);
                console.error("📄 stdout:", stdout);
                console.error("📄 stderr:", stderr);
                reject(err);
            })
            .on("end", () => {
                console.log("✅ Conversion complete:", oggPath);
                resolve();
            })
            .save(oggPath);
    });
}

// Language selection command
bot.command("change_language", (ctx) => {
    const keyboard = {
        inline_keyboard: [
            [
                { text: "🇮🇳 Hindi", callback_data: "lang_hindi" },
                { text: "🇺🇸 English", callback_data: "lang_english" },
            ],
        ],
    };

    ctx.reply("🌐 Choose your input language:", { reply_markup: keyboard });
});

// Handle language selection
bot.action("lang_hindi", (ctx) => {
    const userId = ctx.from.id;
    userLanguages.set(userId, "hindi");
    ctx.answerCbQuery();
    ctx.editMessageText(
        "✅ Language set to Hindi! 🇮🇳\nSend me a voice message in Hindi."
    );
});

bot.action("lang_english", (ctx) => {
    const userId = ctx.from.id;
    userLanguages.set(userId, "english");
    ctx.answerCbQuery();
    ctx.editMessageText(
        "✅ Language set to English! 🇺🇸\nSend me a voice message in English."
    );
});

// Start command
bot.start((ctx) => {
    const userId = ctx.from.id;
    // Set default language to Hindi
    userLanguages.set(userId, "hindi");

    ctx.reply(
        "🎙️ Welcome to the Voice AI Bot!\n\n" +
            "🗣️ Send me a voice message and I'll respond with AI-generated audio!\n\n" +
            "🌐 Default language: Hindi\n" +
            "Use /change_language to switch between Hindi and English input.\n\n" +
            "📝 Current workflow:\n" +
            "• Hindi: Voice → Hindi Text → English → GPT → Hindi → Voice\n" +
            "• English: Voice → English Text → GPT → Hindi → Voice"
    );
});

// Voice message handler
bot.on("voice", async (ctx) => {
    const userId = ctx.from.id;
    const userLanguage = userLanguages.get(userId) || "hindi"; // Default to Hindi

    const fileId = ctx.message.voice.file_id;
    const fileLink = await ctx.telegram.getFileLink(fileId);
    const uid = uuidv4();
    const ogaPath = `./tmp/${uid}.oga`;
    const wavPath = `./tmp/${uid}.wav`;
    const oggPath = `./tmp/${uid}.ogg`;

    const res = await fetch(fileLink.href);
    const buffer = await res.buffer();
    writeFileSync(ogaPath, buffer);

    // Send thinking message
    const thinkingMessage = await ctx.reply("💬 Thinking...");

    try {
        await convertOgaToWav(ogaPath, wavPath);
        const audioBase64 = readFileSync(wavPath).toString("base64");

        let englishPrompt;
        let recognizedText;

        if (userLanguage === "hindi") {
            // Hindi workflow
            recognizedText = await getTextFromSpeechHindi(audioBase64);
            console.log("🗣 Hindi STT:", recognizedText);

            englishPrompt = await translateHindiToEnglish(recognizedText);
            console.log("🔁 EN Prompt:", englishPrompt);
        } else {
            // English workflow
            recognizedText = await getTextFromSpeechEnglish(audioBase64);
            console.log("🗣 English STT:", recognizedText);

            englishPrompt = recognizedText; // Use English text directly
            console.log("🔁 EN Prompt:", englishPrompt);
        }

        const gptReply = await getGptResponse(englishPrompt);
        console.log("🤖 GPT Output:", gptReply);

        const hindiReply = await translateEnglishToHindi(gptReply);
        console.log("📝 Hindi Output:", hindiReply);

        const ttsBase64 = await getSpeechFromTextHindi(hindiReply);
        await saveBase64ToWav(ttsBase64, wavPath);
        await convertWavToOgg(wavPath, oggPath);

        // Delete thinking message
        await ctx.deleteMessage(thinkingMessage.message_id);

        // Send voice response with Hindi text and recognized input
        const inputFlag = userLanguage === "hindi" ? "🇮🇳" : "🇺🇸";
        const caption = `${inputFlag} Input: "${recognizedText}"\n🗣️ ${hindiReply}`;

        await ctx.replyWithVoice(
            { source: createReadStream(oggPath) },
            { caption: caption }
        );
    } catch (err) {
        console.error("❌ Error:", err);
        // Delete thinking message on error
        try {
            await ctx.deleteMessage(thinkingMessage.message_id);
        } catch (deleteErr) {
            console.error("Failed to delete thinking message:", deleteErr);
        }
        ctx.reply("❌ Something went wrong processing your voice message.");
    } finally {
        [ogaPath, wavPath, oggPath].forEach(
            (f) => existsSync(f) && unlinkSync(f)
        );
    }
});

bot.launch();
console.log("🎙️ Multi-language Voice AI Bot is running...");
