import { config as dotenvConfig } from "dotenv";
import TelegramBot from "node-telegram-bot-api";
import fs from "fs";
import { v4 as uuidv4 } from "uuid";
import fetch from "node-fetch";
import ffmpeg from "fluent-ffmpeg";
import { ChatOpenAI } from "@langchain/openai";

dotenvConfig();

const bot = new TelegramBot(process.env.TELEGRAM_BOT_TOKEN, { polling: true });

console.log("🤖 Bot is running...");

// Step 1: Get English response from GPT-4o-mini
async function getLLMResponse(prompt) {
    console.log("💬 Getting LLM response...");
    const model = new ChatOpenAI({
        openAIApiKey: process.env.OPENAI_API_KEY,
        modelName: "gpt-4o-mini", // gpt-4o-mini maps to gpt-4o
    });

    const res = await model.invoke(prompt);
    console.log("📥 GPT Response:", res.content);
    return res.content;
}

// Step 2: Translate English → Hindi using Bhashini
async function translateToHindi(englishText) {
    console.log("🌐 Translating to Hindi...");
    const res = await fetch(
        "https://anuvaad-backend.bhashini.co.in/v1/pipeline",
        {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                Accept: "application/json, text/plain, */*",
            },
            body: JSON.stringify({
                pipelineTasks: [
                    {
                        taskType: "translation",
                        config: {
                            language: {
                                sourceLanguage: "en",
                                targetLanguage: "hi",
                                sourceScriptCode: "Latn",
                                targetScriptCode: "Deva",
                            },
                            serviceId: "ai4bharat/indictrans-v2-all-gpu--t4",
                        },
                    },
                ],
                inputData: {
                    input: [{ source: englishText }],
                    audio: [],
                },
            }),
        }
    );

    const data = await res.json();
    const translated = data.pipelineResponse[0].output[0].target;
    console.log("📝 Translated:", translated);
    return translated;
}

// Step 3: Get TTS audio in WAV
async function generateTTS(hindiText, wavPath) {
    console.log("🔊 Generating speech audio...");
    const res = await fetch(
        "https://anuvaad-backend.bhashini.co.in/v1/pipeline",
        {
            method: "POST",
            headers: {
                Accept: "application/json, text/plain, */*",
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                pipelineTasks: [
                    {
                        taskType: "tts",
                        config: {
                            language: { sourceLanguage: "hi" },
                            serviceId: "Bhashini/IITM/TTS",
                            gender: "female",
                            preProcessors: [],
                            postProcessors: [],
                        },
                    },
                ],
                inputData: {
                    input: [{ source: hindiText }],
                    audio: [{ audioContent: "" }],
                },
            }),
        }
    );

    const data = await res.json();
    const base64Audio = data.pipelineResponse[0].audio[0].audioContent;

    fs.writeFileSync(wavPath, Buffer.from(base64Audio, "base64"));
    console.log("📁 WAV saved:", wavPath);
}

// Step 4: Convert WAV → OGG using FFmpeg
async function convertToOgg(wavPath, oggPath) {
    console.log("🎛️ Converting to OGG...");
    return new Promise((resolve, reject) => {
        ffmpeg(wavPath)
            .inputFormat("wav")
            .audioCodec("libopus") // ✅ use opus instead of vorbis
            .audioFrequency(48000) // ✅ resample to 48kHz for compatibility
            .audioChannels(1)
            .format("ogg")
            .on("start", (cmd) => console.log("🛠 FFmpeg started:", cmd))
            .on("error", (err, stdout, stderr) => {
                console.error("❌ FFmpeg error:", err.message);
                console.error("📄 stdout:", stdout);
                console.error("📄 stderr:", stderr);
                reject(err);
            })
            .on("end", () => {
                console.log("✅ Conversion complete:", oggPath);
                resolve();
            })
            .save(oggPath);
    });
}

// Full pipeline
bot.on("message", async (msg) => {
    const chatId = msg.chat.id;
    const prompt = msg.text;

    if (!prompt) return;

    const id = uuidv4();
    const wavPath = `./tmp/${id}.wav`;
    const oggPath = `./tmp/${id}.ogg`;

    try {
        await bot.sendMessage(chatId, "🧠 Thinking...");
        const response = await getLLMResponse(prompt);

        const hindi = await translateToHindi(response);
        await generateTTS(hindi, wavPath);
        await convertToOgg(wavPath, oggPath);

        await bot.sendVoice(chatId, fs.createReadStream(oggPath), {
            caption: `🗣️ ${hindi}`,
        });

        // Cleanup
        fs.unlinkSync(wavPath);
        fs.unlinkSync(oggPath);
    } catch (err) {
        console.error("💥 Error:", err);
        await bot.sendMessage(chatId, "⚠️ Something went wrong.");
    }
});
